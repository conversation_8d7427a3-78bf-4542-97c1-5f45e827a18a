﻿using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 设备基础信息查询界面
    /// </summary>
    public partial class UC_DeviceInfoQuery : UC_DataQueryBase2
    {
        public UC_DeviceInfoQuery()
        {
            InitializeComponent();
            QueryDataName = "设备基础信息";
        }

        protected override void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("TestTime", "测试时间");
            dgvRecords.Columns.Add("SNCode", "设备序列号");
            dgvRecords.Columns.Add("MainBoardVersion", "主板软件版本");
            dgvRecords.Columns.Add("HmiVersion", "屏幕软件版本");
            dgvRecords.Columns.Add("FlowVersion", "流路版本");
            dgvRecords.Columns.Add("PumpVersion", "柱塞泵软件版本");
            dgvRecords.Columns.Add("PumpSn", "柱塞泵序列号");
            dgvRecords.Columns.Add("ValveVersion", "选向阀软件版本");
            dgvRecords.Columns.Add("ValveSn", "选向阀序列号");
        }

        protected override int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<DeviceInfo>()
                           .Where(data => data.TestTime >= StartTime && data.TestTime <= EndTime);

            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        protected override void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<DeviceInfo>();
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }
                var dataList = query.ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");

                int index = 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["TestTime"].Value = data.TestTime;
                    dr.Cells["SNCode"].Value = data.SNCode;
                    dr.Cells["MainBoardVersion"].Value = data.MainBoardVersion;
                    dr.Cells["HmiVersion"].Value = data.HmiVersion;
                    dr.Cells["FlowVersion"].Value = data.FlowVersion;
                    dr.Cells["PumpVersion"].Value = data.PumpVersion;
                    dr.Cells["PumpSn"].Value = data.PumpSN;
                    dr.Cells["ValveVersion"].Value = data.ValveVersion;
                    dr.Cells["ValveSn"].Value = data.ValveSN;

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index++}/{dataList.Count}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    // 线程切换，防止最终进度界面无法关闭
                    //Thread.Sleep(1);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }
    }
}