﻿using LibBusinessModules.Report.Config;
using LibBusinessModules.Report.Helper;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LibBusinessModules.Report.UI
{
    /// <summary>
    /// 报表导出
    /// </summary>
    public partial class UC_ReportExport : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 报表导出数据
        /// </summary>
        private DeviceRawReportData _reportData;

        #endregion

        #region 构造

        public UC_ReportExport()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 开始查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;

                if(string.IsNullOrEmpty(uc_DeviceSelect.SelectSNCode))
                {
                    throw new FormatException("待查询设备不可为空！");
                }

                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(ParentForm, "数据查询中，请稍候...");

                // 生成数据报告查询队形
                var reportData = new DeviceRawReportData(uc_DeviceSelect.SelectSNCode);
                // 从PC数据库提取数据
                reportData.GetDeviceDataFromDB();

                // 计算数据（选择好数据后才计算）
                reportData.GetDeviceDataFromDB();

                // 查询成功，赋值
                _reportData = reportData;

                // 刷新数据概览
                RefreshDataOverview();

                // 刷新数据详情
                uc_ReportDataShow.LoadReportData(_reportData);
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询设备数据出错：" + ex.Message);
            }
            finally
            {
                Enabled = true;
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        /// <summary>
        /// 导出报表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReportExport_Click(object sender, EventArgs e)
        {
            try
            {
                if(!(chkDataRecord.Checked || chkInspectionRecord.Checked || chkQualifiedRecord.Checked))
                {
                    throw new FormatException("请至少选择一项导出报表类型！");
                }

                // 获取报表计算所需数据
                ReportCalculateNode calculateNode = uc_ReportDataShow.GetReportCalculateData();

                // 执行计算
                calculateNode.CalculatedData();

                // 存储导出的文件路径
                List<string> exportedFilePaths = new List<string>();

                // 导出数据记录单
                if(chkDataRecord.Checked)
                {
                    var filePath = DataRecordExportHelper.ExportWordFile(calculateNode);
                    exportedFilePaths.Add(filePath);
                }

                // 导出出厂检验记录单
                if(chkInspectionRecord.Checked)
                {
                    var filePath = InspectionRecordExportHelper.ExportWordFile(calculateNode);
                    exportedFilePaths.Add(filePath);
                }

                // 导出合格证
                if(chkQualifiedRecord.Checked)
                {
                    var filePath = CertificateExportHelper.ExportWordFile(calculateNode);
                    exportedFilePaths.Add(filePath);
                }

                // 提示是否打开目录
                if(exportedFilePaths.Count > 0)
                {
                    // 获取所有文件的目录路径
                    var directoryPaths = exportedFilePaths
                        .Select(filePath => System.IO.Path.GetDirectoryName(filePath))
                        .Distinct()
                        .ToList();

                    if(UIMessageBox.ShowAsk("报表导出成功，是否打开文件所在目录？"))
                    {
                        // 如果所有文件的目录路径相同，只打开一个
                        if(directoryPaths.Count == 1)
                        {
                            System.Diagnostics.Process.Start("explorer.exe", directoryPaths[0]);
                        }
                        // 如果目录路径不同，依次打开所有目录
                        else
                        {
                            foreach(var directoryPath in directoryPaths)
                            {
                                System.Diagnostics.Process.Start("explorer.exe", directoryPath);
                            }
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("导出报表出错：" + ex.Message);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据概览
        /// </summary>
        private void RefreshDataOverview()
        {
            //曲线数据量
            int curveDataCount = _reportData.CurveDataList.Count;
            // 测量数据量
            int measureDataCount = 0;
            // 校准数据量
            int calibrationDataCount = 0;

            // 高指
            if(_reportData.DevInfo.IsIMNDevice)
            {
                measureDataCount = _reportData.ImnMeasureDataList.Count;
                calibrationDataCount = _reportData.ImnCalibrationDataList.Count;
            }
            // 总氮
            else if(_reportData.DevInfo.IsTNDevice)
            {
                measureDataCount = _reportData.TnMeasureDataList.Count;
                calibrationDataCount = _reportData.TnCalibrationDataList.Count;
            }
            // 常规
            else
            {
                measureDataCount = _reportData.MeasureDataList.Count;
                calibrationDataCount = _reportData.CalibrationDataList.Count;
            }

            // 显示数据量
            txtCurveDataCount.Text = curveDataCount.ToString();
            txtMeasureDataCount.Text = measureDataCount.ToString();
            txtCalibrationDataCount.Text = calibrationDataCount.ToString();
        }

        #endregion
    }
}
