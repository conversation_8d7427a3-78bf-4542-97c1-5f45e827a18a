using LibBaseModules.DB;
using LibBaseModules.Helper;
using LibBusinessModules.Config;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.Helper;
using SqlSugar;
using System;
using System.Threading;

namespace LibBusinessModules.DB
{
    /// <summary>
    /// 数据库访问帮助类
    /// </summary>
    public static class DBHelper
    {
        #region 字段属性

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        private static string _pcDbConnectString;

        /// <summary>
        /// 数据库类型
        /// </summary>
        private static DbType _dbType;

        #endregion

        #region 公共方法

        #region 获取访问器

        /// <summary>
        /// Sqlite数据库访问器
        /// </summary>
        public static SqlSugarClient GetSqliteDBContext(string connectString)
        {
            return SqlSugarHelper.GetDBContext(DbType.Sqlite, connectString);
        }

        /// <summary>
        /// Mysql数据库访问器
        /// </summary>
        public static SqlSugarClient GetMySqlDBContext(string connectString)
        {
            return SqlSugarHelper.GetDBContext(DbType.MySql, connectString);
        }

        /// <summary>
        /// PC数据库访问器
        /// </summary>
        public static SqlSugarClient GetPCDBContext()
        {
            if(string.IsNullOrEmpty(_pcDbConnectString))
            {
                GetPCConnectInfo(out _pcDbConnectString, out _dbType);
            }

            return SqlSugarHelper.GetDBContext(_dbType, _pcDbConnectString);
        }

        #endregion

        /// <summary>
        /// 获取数据库类型
        /// </summary>
        /// <returns></returns>
        public static DbType GetDbType()
        {
            return _dbType;
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        /// <exception cref="Exception"></exception>
        public static void InitializeDB()
        {
            try
            {
                // 记录数据库初始化开始
                LogManager.LogInfo("开始初始化数据库", "系统", "数据库管理", "初始化");

                // 创建数据库
                CreateDB();

                // 表结构同步
                CreateDataTable();

                // 初始化表内容
                InitDataTableInfo();

                // 记录数据库初始化成功
                LogManager.LogInfo("数据库初始化完成", "系统", "数据库管理", "初始化");
            }
            catch(Exception ex)
            {
                // 记录数据库初始化失败
                LogManager.LogError($"初始化软件数据库失败：{ex.Message}", "系统", "数据库管理", "初始化", ex);
                LogUtil.GetInstance().LogWrite($"初始化软件数据库失败：{ex.Message}");
                throw new Exception($"初始化软件数据库失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取PC数据库连接字符串
        /// </summary>
        /// <returns></returns>
        private static void GetPCConnectInfo(out string connectInfo, out DbType dbType)
        {
            SystemConfig.GetInstance().DBInfo.GetConnectInfo(out connectInfo, out dbType);
        }

        /// <summary>
        /// 创建数据库
        /// </summary>
        private static void CreateDB()
        {
            int count = 0;
            while(true)
            {
                try
                {
                    //创建数据库
                    GetPCDBContext().DbMaintenance.CreateDatabase();
                    break;
                }
                catch(Exception ex)
                {
                    Thread.Sleep(2000);
                    count++;
                    if(count > 10)
                    {
                        LogUtil.GetInstance().LogWrite("创建数据库出错：" + ex.Message);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 表结构同步
        /// </summary>
        private static void CreateDataTable()
        {
            try
            {
                // 多表同步
                GetPCDBContext().CodeFirst.SetStringDefaultLength(50).InitTables(typeof(CalibrationData), typeof(CurveData),
                    typeof(AddCheckData), typeof(DoubleCheckData), typeof(SpanCheckData), typeof(ZeroCheckData),
                    typeof(DeviceInfo), typeof(ImnCalibrationData), typeof(ImnMeasureData), typeof(LightSourceInfo), typeof(MeasureData),
                    typeof(TnCalibrationData), typeof(TnMeasureData), typeof(OperData), typeof(AlarmData), typeof(FaultRecordData),
                    typeof(SystemLogData));
            }
            catch(Exception ex)
            {
                LogUtil.GetInstance().LogWrite("同步数据库表结构出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 初始化部分表内容
        /// </summary>
        private static void InitDataTableInfo()
        {
            InitTradeInfo();
        }

        #region 具体表初始化

        /// <summary>
        /// 初始化XXX表
        /// </summary>
        private static void InitTradeInfo()
        {
            try
            {
                //List<PrimaryTradeInfo> trInfos = new List<PrimaryTradeInfo>
                //{
                //    new PrimaryTradeInfo { Id = 1, Name = "食品行业" },
                //    new PrimaryTradeInfo { Id = 2, Name = "日化行业" },
                //    new PrimaryTradeInfo { Id = 3, Name = "印染行业" }
                //};

                //if(GetMySqlDBContext().Insertable(trInfos).ExecuteCommand() <= 0)
                //{
                //    throw new Exception("数据库执行报错！");
                //}
            }
            catch(Exception ex)
            {
                LogUtil.GetInstance().LogWrite("初始化信息表出错：" + ex.Message);
            }
        }

        #endregion

        #endregion
    }
}