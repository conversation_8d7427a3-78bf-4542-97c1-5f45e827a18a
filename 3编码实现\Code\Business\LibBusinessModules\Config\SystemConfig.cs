﻿using LibBaseModules.Json;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace LibBusinessModules.Config
{
    /// <summary>
    /// 系统配置存储类
    /// </summary>
    public class SystemConfig : BaseJsonNode
    {
        #region 字段属性

        #region 用户相关参数

        /// <summary>
        /// 用户名
        /// </summary>
        [JsonIgnore]
        public string UserName = "FAHB";

        /// <summary>
        /// 密码
        /// </summary>
        [Description("密码")]
        public string PassWord { get; set; } = "fahb";

        #endregion

        #region 数据库相关

        /// <summary>
        /// 数据库相关配置
        /// </summary>
        [Description("数据库相关配置")]
        public DBInfo DBInfo { get; set; } = new DBInfo();

        #endregion

        #region FTP相关配置

        /// <summary>
        /// FTP相关配置
        /// </summary>
        [Description("FTP相关配置")]
        public FTPInfo FTPInfo { get; set; } = new FTPInfo();

        #endregion

        #region 质检人员信息管理

        /// <summary>
        /// 员工信息表
        /// </summary>
        public List<EmployeeInfo> EmployeeList = new List<EmployeeInfo>();

        #endregion

        #region 日志相关配置

        /// <summary>
        /// 日志相关配置
        /// </summary>
        [Description("日志相关配置")]
        public LogConfig LogConfig { get; set; } = new LogConfig();

        #endregion

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static SystemConfig _instance = null;
        public static SystemConfig GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new SystemConfig();
                }
            }
            return _instance;
        }

        private SystemConfig()
        {
            loadJson();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 通过工号获取员工信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public EmployeeInfo GetEmployeeById(string id)
        {
            return EmployeeList.FirstOrDefault(x => x.ID == id);
        }

        /// <summary>
        /// 获取所有员工信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<EmployeeInfo> GetAllEmployeeInfo()
        {
            return EmployeeList.OrderBy(x => x.Name).ToList();
        }

        /// <summary>
        /// 重新从本地加载数据
        /// </summary>
        public void ReLoad()
        {
            _instance = null;
        }

        #endregion
    }
}