using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.ReportCenter.Config;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using LiveChartsCore.SkiaSharpView.SKCharts;
using NPOI.Util;
using NPOI.XWPF.UserModel;
using SkiaSharp;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Axis = LiveChartsCore.SkiaSharpView.Axis;

namespace LibBusinessModules.ReportCenter.UI
{
    /// <summary>
    /// 仪表测试量统计面板
    /// </summary>
    public partial class UC_ReportCenter : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 当前选择的时间周期类型
        /// </summary>
        private TimePeriodType _currentPeriodType = TimePeriodType.Day;

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime _startTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime _endTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 统计结果数据
        /// </summary>
        private List<DeviceTrendStatisticsItem> _statisticsData;

        /// <summary>
        /// 设备类型到颜色的映射字典，确保颜色分配的一致性
        /// </summary>
        private static readonly Dictionary<string, SKColor> _deviceTypeColorMap = new Dictionary<string, SKColor>();

        /// <summary>
        /// 预定义的高对比度颜色数组，确保视觉区分度
        /// </summary>
        private static readonly SKColor[] _predefinedColors = new SKColor[]
        {
            SKColor.Parse("#1f77b4"), // 蓝色
            SKColor.Parse("#ff7f0e"), // 橙色
            SKColor.Parse("#2ca02c"), // 绿色
            SKColor.Parse("#d62728"), // 红色
            SKColor.Parse("#9467bd"), // 紫色
            SKColor.Parse("#8c564b"), // 棕色
            SKColor.Parse("#e377c2"), // 粉色
            SKColor.Parse("#7f7f7f"), // 灰色
            SKColor.Parse("#bcbd22"), // 橄榄色
            SKColor.Parse("#17becf"), // 青色
            SKColor.Parse("#aec7e8"), // 浅蓝色
            SKColor.Parse("#ffbb78"), // 浅橙色
            SKColor.Parse("#98df8a"), // 浅绿色
            SKColor.Parse("#ff9896"), // 浅红色
            SKColor.Parse("#c5b0d5"), // 浅紫色
            SKColor.Parse("#c49c94"), // 浅棕色
            SKColor.Parse("#f7b6d3"), // 浅粉色
            SKColor.Parse("#c7c7c7"), // 浅灰色
            SKColor.Parse("#dbdb8d"), // 浅橄榄色
            SKColor.Parse("#9edae5")  // 浅青色
        };

        /// <summary>
        /// 用于生成额外颜色的锁对象
        /// </summary>
        private static readonly object _colorMapLock = new object();

        #endregion

        #region 构造函数

        public UC_ReportCenter()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件处理

        private void UC_DeviceTrendStatistics_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitializeDateTimePickers();
                InitializeChartSettings();
                UpdateDateSelectorVisibility();
                UpdateCurrentPeriodLabel();
            }
        }

        /// <summary>
        /// 时间周期选择变更事件
        /// </summary>
        private void rbPeriodType_CheckedChanged(object sender, EventArgs e)
        {
            var radioButton = sender as UIRadioButton;
            if(radioButton != null && radioButton.Checked)
            {
                if(radioButton == rbYear)
                    _currentPeriodType = TimePeriodType.Year;
                else if(radioButton == rbMonth)
                    _currentPeriodType = TimePeriodType.Month;
                else if(radioButton == rbWeek)
                    _currentPeriodType = TimePeriodType.Week;
                else if(radioButton == rbDay)
                    _currentPeriodType = TimePeriodType.Day;

                UpdateDateSelectorVisibility();
                UpdateCurrentPeriodLabel();
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;

                ClearDisplay();

                // 根据当前周期类型设置查询时间范围
                SetQueryTimeRange();

                if(_startTime >= _endTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }

                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据统计中，请稍候...");

                // 计算统计数据
                CalculateDeviceTrendStatistics();

                // 显示统计结果
                DisplayTrendChart();

                // 显示数据表格
                DisplayDataTable();

                // 显示饼图
                DisplayPieChart();

                // 更新当前周期标签
                UpdateCurrentPeriodLabel();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("统计计算出错:" + ex.Message);
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                Enabled = true;
            }
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExportReport_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                if(_statisticsData == null || _statisticsData.Count == 0)
                {
                    throw new Exception("无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Application.StartupPath + "\\query\\";
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                saveFileDialog.InitialDirectory = filePath;
                var periodText = GetPeriodTypeText(_currentPeriodType);
                saveFileDialog.FileName = $"{filePath}仪表测试量统计报告_{periodText}_{_startTime:yyyy-MM-dd}_{_endTime:yyyy-MM-dd}.docx";

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 显示等待界面
                    UIFormServiceHelper.ShowWaitForm(ParentForm, "正在导出报告文档，请稍候...");

                    // 导出Word文档
                    ExportToWordDocument(saveFileDialog.FileName);

                    // 隐藏等待界面
                    UIFormServiceHelper.HideWaitForm(ParentForm);

                    if(UIMessageBox.ShowAsk("导出成功！是否定位到文件所在位置？"))
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }
            }
            catch(Exception ex)
            {
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
                UIMessageBox.ShowError($"导出失败：{ex.Message}");
            }
            finally
            {
                Enabled = true;
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 清空当前显示的统计数据和图表
        /// </summary>
        private void ClearDisplay()
        {
            dgvStatistics.DataSource = null;
            pieChart.Series = null;
            trendChart.Series = null;
        }

        /// <summary>
        /// 初始化图表设置，配置中文字体支持
        /// </summary>
        private void InitializeChartSettings()
        {
            try
            {
                // 设置LiveCharts全局字体
                LiveCharts.Configure(config =>
                    config.HasGlobalSKTypeface(SKTypeface.FromFamilyName("Microsoft YaHei"))
                );

                // 设置图表标题
                trendChart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = "仪表测试量趋势图",
                    TextSize = 16,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };

                // 设置饼图标题
                pieChart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = "仪表类型分布",
                    TextSize = 16,
                    HorizontalAlignment = LiveChartsCore.Drawing.Align.Middle,
                    VerticalAlignment = LiveChartsCore.Drawing.Align.Start,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };
            }
            catch(Exception ex)
            {
                // 如果字体设置失败，记录错误但不影响程序运行
                UINotifierHelper.ShowNotifier($"图表初始化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitializeDateTimePickers()
        {
            var today = DateTime.Today;

            // 设置默认值
            dtpStartTime.Value = today.AddDays(-1);
            dtpEndTime.Value = today;

            // 周模式：当前周
            var currentWeek = GetWeekOfYear(today);
            nudWeekNumber.Value = currentWeek;
            nudWeekNumber.ValueChanged += nudWeekNumber_ValueChanged;
        }

        /// <summary>
        /// 周数选择器值变更事件
        /// </summary>
        private void nudWeekNumber_ValueChanged(object sender, int value)
        {
            UpdateCurrentPeriodLabel();
        }

        /// <summary>
        /// 更新日期选择器的可见性
        /// </summary>
        private void UpdateDateSelectorVisibility()
        {
            // 隐藏所有控件
            nudWeekNumber.Visible = false;
            dtpStartTime.Visible = false;
            dtpEndTime.Visible = false;
            lblMiddleText.Visible = false;
            lblLastText.Visible = false;

            var tempDate = dtpStartTime.Value;
            // 根据当前周期类型设置dtpStartTime的ShowType和显示对应控件
            switch(_currentPeriodType)
            {
                case TimePeriodType.Year:
                    dtpStartTime.ShowType = UIDateType.Year;
                    dtpStartTime.Value = tempDate;
                    dtpStartTime.Visible = true;
                    break;
                case TimePeriodType.Month:
                    dtpStartTime.ShowType = UIDateType.YearMonth;
                    dtpStartTime.Value = tempDate;
                    dtpStartTime.Visible = true;
                    break;
                case TimePeriodType.Week:
                    dtpStartTime.ShowType = UIDateType.Year;
                    dtpStartTime.Value = tempDate;
                    dtpStartTime.Visible = true;
                    nudWeekNumber.Visible = true;
                    lblMiddleText.Visible = true;
                    lblLastText.Visible = true;
                    // 更新年份显示
                    var currentYear = DateTime.Today.Year;
                    lblMiddleText.Text = "第";
                    break;
                case TimePeriodType.Day:
                    dtpStartTime.ShowType = UIDateType.YearMonthDay;
                    dtpStartTime.Value = tempDate;
                    dtpStartTime.Visible = true;
                    dtpEndTime.Visible = true;
                    lblMiddleText.Visible = true;
                    lblMiddleText.Text = "至";
                    break;
            }
        }

        /// <summary>
        /// 更新当前统计周期标签
        /// </summary>
        private void UpdateCurrentPeriodLabel()
        {
            string periodText = "";

            switch(_currentPeriodType)
            {
                case TimePeriodType.Year:
                    var year = dtpStartTime.Value.Year;
                    var yearStart = new DateTime(year, 1, 1);
                    var yearEnd = new DateTime(year, 12, 31);
                    periodText = $"{year}年 ({yearStart:yyyy年MM月dd日}-{yearEnd:yyyy年MM月dd日})";
                    break;
                case TimePeriodType.Month:
                    var monthStart = new DateTime(dtpStartTime.Value.Year, dtpStartTime.Value.Month, 1);
                    var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                    periodText = $"{dtpStartTime.Value:yyyy年MM月} ({monthStart:yyyy年MM月dd日}-{monthEnd:yyyy年MM月dd日})";
                    break;
                case TimePeriodType.Week:
                    var currentYear = dtpStartTime.Value.Year;
                    var weekNumber = (int)nudWeekNumber.Value;
                    var weekStart = GetFirstDayOfWeek(currentYear, weekNumber);
                    var weekEnd = weekStart.AddDays(6);
                    periodText = $"{currentYear}年第{weekNumber}周 ({weekStart:yyyy年MM月dd日}-{weekEnd:yyyy年MM月dd日})";
                    break;
                case TimePeriodType.Day:
                    periodText = $"{dtpStartTime.Value:yyyy年MM月dd日} 至 {dtpEndTime.Value:yyyy年MM月dd日}";
                    break;
            }

            lblCurrentPeriod.Text = $"当前统计周期：{periodText}";
        }

        /// <summary>
        /// 获取周期类型的文本描述
        /// </summary>
        private string GetPeriodTypeText(TimePeriodType periodType)
        {
            switch(periodType)
            {
                case TimePeriodType.Year: return "年统计";
                case TimePeriodType.Month: return "月统计";
                case TimePeriodType.Week: return "周统计";
                case TimePeriodType.Day: return "日统计";
                default: return "统计";
            }
        }

        /// <summary>
        /// 获取指定日期是一年中的第几周
        /// </summary>
        private int GetWeekOfYear(DateTime date)
        {
            var culture = CultureInfo.CurrentCulture;
            var calendar = culture.Calendar;
            return calendar.GetWeekOfYear(date, culture.DateTimeFormat.CalendarWeekRule, culture.DateTimeFormat.FirstDayOfWeek);
        }

        /// <summary>
        /// 获取指定年份和周数的第一天
        /// </summary>
        private DateTime GetFirstDayOfWeek(int year, int weekNumber)
        {
            var jan1 = new DateTime(year, 1, 1);
            var daysOffset = DayOfWeek.Monday - jan1.DayOfWeek;
            var firstMonday = jan1.AddDays(daysOffset);
            var cal = CultureInfo.CurrentCulture.Calendar;
            var firstWeek = cal.GetWeekOfYear(jan1, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);

            if(firstWeek <= 1)
            {
                weekNumber -= 1;
            }

            return firstMonday.AddDays(weekNumber * 7);
        }

        /// <summary>
        /// 显示数据表格（仪表类型作为行，数据量作为列）
        /// </summary>
        private void DisplayDataTable()
        {
            if(_statisticsData == null || _statisticsData.Count == 0)
            {
                dgvStatistics.DataSource = null;
                return;
            }

            try
            {
                // 获取所有仪表类型和时间标签
                var deviceTypes = _statisticsData.Select(x => x.DeviceType).Distinct().OrderBy(x => x == "总数量" ? "Z" : x).ToList();
                var timeLabels = _statisticsData.Select(x => x.TimeLabel).Distinct().OrderBy(x => x).ToList();

                // 创建数据表
                var dataTable = new DataTable();
                dataTable.Columns.Add("仪表类型", typeof(string));

                // 添加汇总列
                dataTable.Columns.Add("数量", typeof(int));

                // 填充数据（仪表类型作为行）
                foreach(var deviceType in deviceTypes)
                {
                    var row = dataTable.NewRow();
                    row["仪表类型"] = deviceType;

                    // 计算该仪表类型的汇总
                    int total = _statisticsData.Where(x => x.DeviceType == deviceType).Sum(x => x.DeviceCount);
                    row["数量"] = total;

                    dataTable.Rows.Add(row);
                }

                // 清除现有列
                dgvStatistics.Columns.Clear();

                // 设置数据源
                dgvStatistics.DataSource = dataTable;

                // 设置列样式
                foreach(DataGridViewColumn column in dgvStatistics.Columns)
                {
                    column.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    // 禁止点击排序
                    column.SortMode = DataGridViewColumnSortMode.NotSortable;

                    // 仪表类型列左对齐
                    if(column.Name == "仪表类型")
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
                        column.DefaultCellStyle.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold);
                    }

                    // 汇总列特殊样式
                    if(column.Name == "数量")
                    {
                        column.DefaultCellStyle.BackColor = System.Drawing.Color.LightBlue;
                        column.DefaultCellStyle.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold);
                    }
                }

                // 设置总数量行样式
                foreach(DataGridViewRow row in dgvStatistics.Rows)
                {
                    if(row.Cells["仪表类型"].Value?.ToString() == "总数量")
                    {
                        row.DefaultCellStyle.BackColor = System.Drawing.Color.LightYellow;
                        row.DefaultCellStyle.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold);
                        break;
                    }
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"显示数据表格失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据当前周期类型设置查询时间范围
        /// </summary>
        private void SetQueryTimeRange()
        {
            switch(_currentPeriodType)
            {
                case TimePeriodType.Year:
                    var year = dtpStartTime.Value.Year;
                    _startTime = new DateTime(year, 1, 1);
                    _endTime = new DateTime(year, 12, 31, 23, 59, 59);
                    break;
                case TimePeriodType.Month:
                    var monthStart = new DateTime(dtpStartTime.Value.Year, dtpStartTime.Value.Month, 1);
                    _startTime = monthStart;
                    _endTime = monthStart.AddMonths(1).AddSeconds(-1);
                    break;
                case TimePeriodType.Week:
                    var currentYear = dtpStartTime.Value.Year;
                    var weekNumber = (int)nudWeekNumber.Value;
                    var weekStart = GetFirstDayOfWeek(currentYear, weekNumber);
                    _startTime = weekStart;
                    _endTime = weekStart.AddDays(7).AddSeconds(-1);
                    break;
                case TimePeriodType.Day:
                    _startTime = dtpStartTime.Value.Date;
                    _endTime = dtpEndTime.Value.Date.AddDays(1).AddSeconds(-1);
                    break;
            }
        }

        /// <summary>
        /// 计算仪表趋势统计数据
        /// </summary>
        private void CalculateDeviceTrendStatistics()
        {
            _statisticsData = new List<DeviceTrendStatisticsItem>();

            // 获取指定时间段内的仪表记录
            var deviceRecords = DBHelper.GetPCDBContext().Queryable<DeviceInfo>()
                     .Where(data => data.TestTime >= _startTime && data.TestTime <= _endTime)
                     .ToList();

            if(deviceRecords.Count == 0)
            {
                throw new Exception("指定时间段内没有仪表数据！");
            }

            // 生成时间点列表
            var timePoints = GenerateTimePoints();

            // 获取所有仪表类型
            var deviceTypes = deviceRecords.Select(d => d.GetDeviceFactor()).Distinct().ToList();
            deviceTypes.Add("总数量"); // 添加总数量统计

            // 为每个时间点和仪表类型计算数量
            foreach(var timePoint in timePoints)
            {
                var timeStart = timePoint;
                var timeEnd = GetTimePointEnd(timePoint);

                // 获取该时间段内的仪表记录
                var periodDevices = deviceRecords.Where(d => d.TestTime >= timeStart && d.TestTime < timeEnd).ToList();

                foreach(var deviceType in deviceTypes)
                {
                    int count;
                    bool isTotal = deviceType == "总数量";

                    if(isTotal)
                    {
                        count = periodDevices.Count;
                    }
                    else
                    {
                        count = periodDevices.Count(d => d.GetDeviceFactor() == deviceType);
                    }

                    _statisticsData.Add(new DeviceTrendStatisticsItem
                    {
                        StatisticsTime = timePoint,
                        TimeLabel = GetTimeLabel(timePoint),
                        DeviceType = deviceType,
                        DeviceCount = count,
                        IsTotal = isTotal
                    });
                }
            }
        }

        /// <summary>
        /// 生成时间点列表
        /// </summary>
        private List<DateTime> GenerateTimePoints()
        {
            var timePoints = new List<DateTime>();

            switch(_currentPeriodType)
            {
                case TimePeriodType.Year:
                    // 按月统计，确保按正确顺序（1月到12月）
                    for(int month = 1; month <= 12; month++)
                    {
                        timePoints.Add(new DateTime(_startTime.Year, month, 1));
                    }
                    break;
                case TimePeriodType.Month:
                    // 按日统计
                    var current = _startTime.Date;
                    while(current <= _endTime.Date)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
                case TimePeriodType.Week:
                    // 按日统计
                    current = _startTime.Date;
                    while(current <= _endTime.Date)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
                case TimePeriodType.Day:
                    // 按日统计
                    current = _startTime.Date;
                    while(current <= _endTime.Date)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
            }

            // 确保时间点按升序排列
            return timePoints.OrderBy(t => t).ToList();
        }

        /// <summary>
        /// 获取时间点的结束时间
        /// </summary>
        private DateTime GetTimePointEnd(DateTime timePoint)
        {
            switch(_currentPeriodType)
            {
                case TimePeriodType.Year:
                    // 月结束时间
                    return timePoint.AddMonths(1);
                case TimePeriodType.Month:
                case TimePeriodType.Week:
                case TimePeriodType.Day:
                    // 日结束时间
                    return timePoint.AddDays(1);
                default:
                    return timePoint.AddDays(1);
            }
        }

        /// <summary>
        /// 获取时间标签
        /// </summary>
        private string GetTimeLabel(DateTime timePoint)
        {
            switch(_currentPeriodType)
            {
                case TimePeriodType.Year:
                    return $"{timePoint.Month}月";
                case TimePeriodType.Month:
                case TimePeriodType.Week:
                case TimePeriodType.Day:
                    return timePoint.ToString("MM-dd");
                default:
                    return timePoint.ToString("MM-dd");
            }
        }

        /// <summary>
        /// 显示趋势图表
        /// </summary>
        private void DisplayTrendChart()
        {
            if(_statisticsData == null || _statisticsData.Count == 0)
            {
                trendChart.Series = null;
                return;
            }

            try
            {
                // 获取所有仪表类型
                var deviceTypes = _statisticsData.Select(x => x.DeviceType).Distinct().ToList();

                // 获取时间标签并按正确顺序排序
                string[] timeLabels;
                if(_currentPeriodType == TimePeriodType.Year)
                {
                    // 年模式：按月份数字排序（1月、2月...12月）
                    var timeData = _statisticsData.Select(x => new { Label = x.TimeLabel, Time = x.StatisticsTime })
                                                 .Distinct()
                                                 .OrderBy(x => x.Time.Month)
                                                 .Select(x => x.Label)
                                                 .ToArray();
                    timeLabels = timeData;
                }
                else
                {
                    // 其他模式：按时间排序
                    timeLabels = _statisticsData.Select(x => new { Label = x.TimeLabel, Time = x.StatisticsTime })
                                               .Distinct()
                                               .OrderBy(x => x.Time)
                                               .Select(x => x.Label)
                                               .ToArray();
                }

                // 创建系列集合
                var seriesList = new List<ISeries>();

                // 为每个仪表类型创建一个折线系列
                foreach(var deviceType in deviceTypes)
                {
                    var typeData = _statisticsData.Where(x => x.DeviceType == deviceType)
                                                 .OrderBy(x => x.StatisticsTime)
                                                 .ToList();

                    var values = new List<double>();
                    foreach(var label in timeLabels)
                    {
                        var item = typeData.FirstOrDefault(x => x.TimeLabel == label);
                        values.Add(item?.DeviceCount ?? 0);
                    }

                    // 为总数量使用不同的颜色和线型
                    var series = new LineSeries<double>
                    {
                        Values = values,
                        Name = deviceType,
                        Fill = null, // 不填充
                        GeometrySize = 8,
                        LineSmoothness = 0, // 直线连接
                        Stroke = deviceType == "总数量" ?
                            new SolidColorPaint(SKColors.Red) { StrokeThickness = 3 } :
                            new SolidColorPaint(GetSeriesColor(deviceType)) { StrokeThickness = 2 },
                        GeometryStroke = deviceType == "总数量" ?
                            new SolidColorPaint(SKColors.Red) { StrokeThickness = 2 } :
                            new SolidColorPaint(GetSeriesColor(deviceType)) { StrokeThickness = 1 },
                        GeometryFill = deviceType == "总数量" ?
                            new SolidColorPaint(SKColors.Red) :
                            new SolidColorPaint(GetSeriesColor(deviceType))
                    };

                    seriesList.Add(series);
                }

                // 设置图表
                trendChart.Series = seriesList;

                // 设置图例
                trendChart.LegendPosition = LiveChartsCore.Measure.LegendPosition.Top;
                trendChart.LegendTextSize = 12;
                trendChart.LegendTextPaint = new SolidColorPaint(SKColors.Black)
                {
                    SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                };

                // 设置X轴
                trendChart.XAxes = new Axis[]
                {
                    new Axis
                    {
                        Labels = timeLabels,
                        LabelsRotation = timeLabels.Length > 10 ? -45 : 0,
                        TextSize = 12,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };

                // 设置Y轴
                trendChart.YAxes = new Axis[]
                {
                    new Axis
                    {
                        Name = "数量",
                        TextSize = 12,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray),
                        MinLimit = 0
                    }
                };
            }
            catch(Exception ex)
            {
                throw new Exception($"显示图表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 显示饼图（仪表类型分布）
        /// </summary>
        private void DisplayPieChart()
        {
            if(_statisticsData == null || _statisticsData.Count == 0)
            {
                pieChart.Series = null;
                return;
            }

            try
            {
                // 获取各仪表类型的总数量（排除"总数量"）
                var deviceTypeTotals = _statisticsData
                    .Where(x => x.DeviceType != "总数量")
                    .GroupBy(x => x.DeviceType)
                    .Select(g => new { DeviceType = g.Key, Total = g.Sum(x => x.DeviceCount) })
                    .Where(x => x.Total > 0)
                    .OrderByDescending(x => x.Total)
                    .ToList();

                if(!deviceTypeTotals.Any())
                {
                    pieChart.Series = null;
                    return;
                }

                // 创建饼图系列
                var seriesList = new List<ISeries>();

                var outer = 0;

                foreach(var item in deviceTypeTotals)
                {
                    var series = new PieSeries<double>
                    {
                        Values = new double[] { item.Total },
                        Name = item.DeviceType,
                        Fill = new SolidColorPaint(GetSeriesColor(item.DeviceType)),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        // 数据标签样式优化
                        DataLabelsSize = 11,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = (point) => $"{item.DeviceType}\r\n{item.Total}台",
                        DataLabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        // 确保数据标签默认显示
                        DataLabelsRotation = 0,
                        // 设置扇形的外半径
                        OuterRadiusOffset = outer,
                        // 设置扇形的内半径，创建环形图效果（可选）
                        //InnerRadius = 15
                    };

                    outer += 10;

                    seriesList.Add(series);
                }

                // 设置饼图
                pieChart.Series = seriesList;

                //// 设置图例（由于空间有限，将图例放在底部）
                //pieChart.LegendPosition = LiveChartsCore.Measure.LegendPosition.Bottom;
                //pieChart.LegendTextSize = 9;
                //pieChart.LegendTextPaint = new SolidColorPaint(SKColors.Black)
                //{
                //    SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                //};
            }
            catch(Exception ex)
            {
                throw new Exception($"显示饼图失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取系列颜色 - 优化版本，确保每个设备类型都有唯一且一致的颜色
        /// </summary>
        /// <param name="deviceType">设备类型名称</param>
        /// <returns>对应的颜色</returns>
        private SKColor GetSeriesColor(string deviceType)
        {
            // 参数验证
            if(string.IsNullOrWhiteSpace(deviceType))
            {
                return SKColors.Gray; // 默认颜色
            }

            // 线程安全地检查和分配颜色
            lock(_colorMapLock)
            {
                // 如果已经分配过颜色，直接返回
                if(_deviceTypeColorMap.TryGetValue(deviceType, out SKColor existingColor))
                {
                    return existingColor;
                }

                // 为新的设备类型分配颜色
                SKColor newColor;
                int currentMappingCount = _deviceTypeColorMap.Count;

                if(currentMappingCount < _predefinedColors.Length)
                {
                    // 使用预定义颜色
                    newColor = _predefinedColors[currentMappingCount];
                }
                else
                {
                    // 预定义颜色用完后，生成新的高对比度颜色
                    newColor = GenerateDistinctColor(currentMappingCount - _predefinedColors.Length);
                }

                // 存储映射关系
                _deviceTypeColorMap[deviceType] = newColor;
                return newColor;
            }
        }

        /// <summary>
        /// 生成具有良好视觉区分度的颜色
        /// </summary>
        /// <param name="index">颜色索引</param>
        /// <returns>生成的颜色</returns>
        private SKColor GenerateDistinctColor(int index)
        {
            // 使用HSV色彩空间生成高对比度颜色
            // 通过调整色相(H)、饱和度(S)、明度(V)来确保颜色的区分度

            // 色相：在色环上均匀分布
            float hue = (index * 137.5f) % 360f; // 使用黄金角度137.5°确保分布均匀

            // 饱和度：在高饱和度范围内变化，确保颜色鲜艳
            float saturation = 0.7f + (index % 3) * 0.1f; // 0.7, 0.8, 0.9 循环

            // 明度：在中等到高明度范围内变化，确保可读性
            float value = 0.8f + (index % 2) * 0.15f; // 0.8, 0.95 交替

            // 将HSV转换为RGB
            return HSVToSKColor(hue, saturation, value);
        }

        /// <summary>
        /// 将HSV颜色空间转换为SKColor
        /// </summary>
        /// <param name="hue">色相 (0-360)</param>
        /// <param name="saturation">饱和度 (0-1)</param>
        /// <param name="value">明度 (0-1)</param>
        /// <returns>对应的SKColor</returns>
        private SKColor HSVToSKColor(float hue, float saturation, float value)
        {
            int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            float f = hue / 60 - (float)Math.Floor(hue / 60);

            value *= 255;
            byte v = Convert.ToByte(value);
            byte p = Convert.ToByte(value * (1 - saturation));
            byte q = Convert.ToByte(value * (1 - f * saturation));
            byte t = Convert.ToByte(value * (1 - (1 - f) * saturation));

            return hi switch
            {
                0 => new SKColor(v, t, p),
                1 => new SKColor(q, v, p),
                2 => new SKColor(p, v, t),
                3 => new SKColor(p, q, v),
                4 => new SKColor(t, p, v),
                _ => new SKColor(v, p, q)
            };
        }

        /// <summary>
        /// 重置设备类型颜色映射（可选功能，用于清理映射缓存）
        /// </summary>
        public static void ResetDeviceTypeColorMapping()
        {
            lock(_colorMapLock)
            {
                _deviceTypeColorMap.Clear();
            }
        }

        /// <summary>
        /// 获取当前已映射的设备类型数量
        /// </summary>
        /// <returns>已映射的设备类型数量</returns>
        public static int GetMappedDeviceTypeCount()
        {
            lock(_colorMapLock)
            {
                return _deviceTypeColorMap.Count;
            }
        }

        /// <summary>
        /// 导出到Word文档
        /// </summary>
        private void ExportToWordDocument(string fileName)
        {
            try
            {
                using(var document = new XWPFDocument())
                {
                    // 1. 添加标题
                    var titleParagraph = document.CreateParagraph();
                    titleParagraph.Alignment = ParagraphAlignment.CENTER;
                    var titleRun = titleParagraph.CreateRun();
                    titleRun.SetText($"仪表测试量统计报告 - {GetPeriodTypeText(_currentPeriodType)}");
                    titleRun.FontSize = 18;
                    titleRun.IsBold = true;
                    titleRun.FontFamily = "Microsoft YaHei";

                    // 2. 添加统计周期信息
                    var periodParagraph = document.CreateParagraph();
                    periodParagraph.Alignment = ParagraphAlignment.CENTER;
                    var periodRun = periodParagraph.CreateRun();
                    periodRun.SetText(lblCurrentPeriod.Text);
                    periodRun.FontSize = 12;
                    periodRun.FontFamily = "Microsoft YaHei";

                    // 3. 添加空行
                    document.CreateParagraph();

                    // 4. 添加折线图
                    ExportTrendChartToDocument(document);

                    // 5. 添加空行
                    document.CreateParagraph();

                    // 6. 添加饼图
                    ExportPieChartToDocument(document);

                    // 7. 添加空行
                    document.CreateParagraph();

                    // 8. 添加数据表格标题
                    var dataTableTitleParagraph = document.CreateParagraph();
                    dataTableTitleParagraph.Alignment = ParagraphAlignment.CENTER;
                    var dataTableTitleRun = dataTableTitleParagraph.CreateRun();
                    dataTableTitleRun.SetText("数据详情");
                    dataTableTitleRun.FontSize = 14;
                    dataTableTitleRun.IsBold = true;
                    dataTableTitleRun.FontFamily = "Microsoft YaHei";

                    // 9. 添加空行
                    document.CreateParagraph();

                    // 10. 添加数据表格
                    CreateDataTable(document);

                    // 保存文档
                    using(var fileStream = new FileStream(fileName, FileMode.Create, FileAccess.Write))
                    {
                        document.Write(fileStream);
                    }
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"导出Word文档失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 在Word文档中创建数据表格
        /// </summary>
        private void CreateDataTable(XWPFDocument document)
        {
            if(_statisticsData == null || _statisticsData.Count == 0)
                return;

            // 获取数据
            var deviceTypes = _statisticsData.Select(x => x.DeviceType).Distinct().OrderBy(x => x == "总数量" ? "Z" : x).ToList();
            var timeLabels = _statisticsData.Select(x => new { Label = x.TimeLabel, Time = x.StatisticsTime })
                                           .Distinct()
                                           .OrderBy(x => x.Time)
                                           .Select(x => x.Label)
                                           .ToList();

            // 创建表格
            var table = document.CreateTable(timeLabels.Count + 2, deviceTypes.Count + 1); // +2 for header and summary, +1 for time column

            // 设置表格样式
            table.Width = 5000;

            // 创建表头并设置样式
            var headerRow = table.GetRow(0);

            // 设置表头第一列（时间列）
            var timeHeaderCell = headerRow.GetCell(0);
            timeHeaderCell.SetText("时间");
            SetCellStyle(timeHeaderCell, true, ParagraphAlignment.CENTER);

            // 设置表头其他列（设备类型列）
            for(int i = 0; i < deviceTypes.Count; i++)
            {
                var headerCell = headerRow.GetCell(i + 1);
                headerCell.SetText(deviceTypes[i]);
                SetCellStyle(headerCell, true, ParagraphAlignment.CENTER);
            }

            // 填充数据行
            for(int rowIndex = 0; rowIndex < timeLabels.Count; rowIndex++)
            {
                var dataRow = table.GetRow(rowIndex + 1);
                var timeLabel = timeLabels[rowIndex];

                // 设置时间列
                var timeCell = dataRow.GetCell(0);
                timeCell.SetText(timeLabel);
                SetCellStyle(timeCell, false, ParagraphAlignment.CENTER);

                // 设置数据列
                for(int colIndex = 0; colIndex < deviceTypes.Count; colIndex++)
                {
                    var deviceType = deviceTypes[colIndex];
                    var item = _statisticsData.FirstOrDefault(x => x.TimeLabel == timeLabel && x.DeviceType == deviceType);
                    var dataCell = dataRow.GetCell(colIndex + 1);
                    dataCell.SetText((item?.DeviceCount ?? 0).ToString());
                    SetCellStyle(dataCell, false, ParagraphAlignment.CENTER);
                }
            }

            // 添加汇总行
            var summaryRow = table.GetRow(timeLabels.Count + 1);

            // 设置汇总行第一列
            var summaryHeaderCell = summaryRow.GetCell(0);
            summaryHeaderCell.SetText("汇总");
            SetCellStyle(summaryHeaderCell, true, ParagraphAlignment.CENTER);

            // 设置汇总行数据列
            for(int colIndex = 0; colIndex < deviceTypes.Count; colIndex++)
            {
                var deviceType = deviceTypes[colIndex];
                var total = _statisticsData.Where(x => x.DeviceType == deviceType).Sum(x => x.DeviceCount);
                var summaryCell = summaryRow.GetCell(colIndex + 1);
                summaryCell.SetText(total.ToString());
                SetCellStyle(summaryCell, true, ParagraphAlignment.CENTER);
            }
        }

        /// <summary>
        /// 设置单元格样式
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <param name="isBold">是否粗体</param>
        /// <param name="alignment">对齐方式</param>
        private void SetCellStyle(XWPFTableCell cell, bool isBold, ParagraphAlignment alignment)
        {
            // 获取单元格的段落
            var paragraphs = cell.Paragraphs;
            if(paragraphs != null && paragraphs.Count > 0)
            {
                var paragraph = paragraphs[0];

                // 设置段落对齐方式
                paragraph.Alignment = alignment;

                // 获取或创建文本运行
                var runs = paragraph.Runs;
                if(runs != null && runs.Count > 0)
                {
                    var run = runs[0];
                    // 设置字体样式
                    run.IsBold = isBold;
                    run.FontFamily = "Microsoft YaHei";
                    run.FontSize = 12;
                }
                else
                {
                    // 如果没有运行，创建一个新的
                    var run = paragraph.CreateRun();
                    run.IsBold = isBold;
                    run.FontFamily = "Microsoft YaHei";
                    run.FontSize = 12;
                }
            }
        }

        /// <summary>
        /// 导出折线图到Word文档
        /// </summary>
        private void ExportTrendChartToDocument(XWPFDocument document)
        {
            try
            {
                if(trendChart.Series != null && trendChart.Series.Any())
                {
                    // 使用LiveCharts的SKCartesianChart导出功能
                    var skChart = new SKCartesianChart(trendChart) { Width = 800, Height = 500 };

                    using(var chartImage = skChart.GetImage())
                    using(var data = chartImage.Encode())
                    using(var stream = new MemoryStream(data.ToArray()))
                    {
                        var paragraph = document.CreateParagraph();
                        paragraph.Alignment = ParagraphAlignment.CENTER;
                        var run = paragraph.CreateRun();

                        run.AddPicture(stream, (int)PictureType.PNG, "trend_chart.png",
                                     Units.ToEMU(400), Units.ToEMU(250));
                    }
                }
                else
                {
                    // 如果没有数据，添加说明文字
                    var paragraph = document.CreateParagraph();
                    paragraph.Alignment = ParagraphAlignment.CENTER;
                    var run = paragraph.CreateRun();
                    run.SetText("暂无折线图数据");
                    run.FontFamily = "Microsoft YaHei";
                }
            }
            catch(Exception ex)
            {
                // 如果折线图导出失败，添加文本说明
                var paragraph = document.CreateParagraph();
                paragraph.Alignment = ParagraphAlignment.CENTER;
                var run = paragraph.CreateRun();
                run.SetText($"折线图导出失败：{ex.Message}");
                run.FontFamily = "Microsoft YaHei";
            }
        }

        /// <summary>
        /// 导出饼图到Word文档
        /// </summary>
        private void ExportPieChartToDocument(XWPFDocument document)
        {
            try
            {
                if(pieChart.Series != null && pieChart.Series.Any())
                {
                    // 使用LiveCharts的SKPieChart导出功能
                    var skChart = new SKPieChart(pieChart) { Width = 500, Height = 500 };

                    using(var chartImage = skChart.GetImage())
                    using(var data = chartImage.Encode())
                    using(var stream = new MemoryStream(data.ToArray()))
                    {
                        var paragraph = document.CreateParagraph();
                        paragraph.Alignment = ParagraphAlignment.CENTER;
                        var run = paragraph.CreateRun();

                        run.AddPicture(stream, (int)PictureType.PNG, "pie_chart.png",
                                     Units.ToEMU(300), Units.ToEMU(300));
                    }
                }
                else
                {
                    // 如果没有数据，添加说明文字
                    var paragraph = document.CreateParagraph();
                    paragraph.Alignment = ParagraphAlignment.CENTER;
                    var run = paragraph.CreateRun();
                    run.SetText("暂无饼图数据");
                    run.FontFamily = "Microsoft YaHei";
                }
            }
            catch(Exception ex)
            {
                // 如果饼图导出失败，添加文本说明
                var paragraph = document.CreateParagraph();
                paragraph.Alignment = ParagraphAlignment.CENTER;
                var run = paragraph.CreateRun();
                run.SetText($"饼图导出失败：{ex.Message}");
                run.FontFamily = "Microsoft YaHei";
            }
        }

        #endregion
    }
}