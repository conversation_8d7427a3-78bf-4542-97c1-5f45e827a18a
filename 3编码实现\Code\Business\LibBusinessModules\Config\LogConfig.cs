using LibBusinessModules.DB.Models.PC;
using System.ComponentModel;

namespace LibBusinessModules.Config
{
    /// <summary>
    /// 日志配置类
    /// </summary>
    public class LogConfig
    {
        #region 字段属性

        /// <summary>
        /// 是否启用日志记录
        /// </summary>
        [Description("是否启用日志记录")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 最小日志级别
        /// </summary>
        [Description("最小日志级别")]
        public LogLevel MinLogLevel { get; set; } = LogLevel.Info;

        /// <summary>
        /// 日志保留天数（0表示不自动清理）
        /// </summary>
        [Description("日志保留天数")]
        public int RetentionDays { get; set; } = 30;

        /// <summary>
        /// 是否记录调试日志
        /// </summary>
        [Description("是否记录调试日志")]
        public bool EnableDebugLog { get; set; } = false;

        /// <summary>
        /// 是否记录客户端IP
        /// </summary>
        [Description("是否记录客户端IP")]
        public bool RecordClientIP { get; set; } = true;

        /// <summary>
        /// 单次查询最大记录数
        /// </summary>
        [Description("单次查询最大记录数")]
        public int MaxQueryRecords { get; set; } = 1000;

        /// <summary>
        /// 分页大小
        /// </summary>
        [Description("分页大小")]
        public int PageSize { get; set; } = 50;

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public LogConfig()
        {
        }

        #endregion
    }
}
