# LogManager 使用示例

## 概述

LogManager 是一个静态日志管理类，提供了完整的数据库日志记录功能。支持不同级别的日志记录，自动获取调用者信息，并提供异步数据库写入功能。

## 基本用法

### 1. 信息日志记录

```csharp
// 基本信息日志
LogManager.LogInfo("用户登录成功");

// 带操作人员的信息日志
LogManager.LogInfo("数据查询完成", "张三");

// 完整参数的信息日志
LogManager.LogInfo("设备校准完成", "张三", "设备管理", "校准操作");
```

### 2. 警告日志记录

```csharp
// 基本警告日志
LogManager.LogWarning("设备连接超时");

// 带操作人员的警告日志
LogManager.LogWarning("数据验证失败", "李四");

// 完整参数的警告日志
LogManager.LogWarning("设备响应异常", "李四", "设备通信", "数据采集");
```

### 3. 错误日志记录

```csharp
// 基本错误日志
LogManager.LogError("数据库连接失败");

// 带异常信息的错误日志
try
{
    // 业务代码
}
catch (Exception ex)
{
    LogManager.LogError("业务操作失败", "王五", "业务模块", "数据处理", ex);
}
```

### 4. 调试日志记录

```csharp
// 调试信息（仅在调试级别启用时记录）
LogManager.LogDebug("进入方法 ProcessData");
LogManager.LogDebug($"参数值：count={count}, name={name}");
```

### 5. 致命错误日志记录

```csharp
// 致命错误（系统级错误）
try
{
    // 关键系统操作
}
catch (Exception ex)
{
    LogManager.LogFatal("系统初始化失败", "系统", "系统管理", "初始化", ex);
}
```

## 实际应用场景

### 用户登录场景

```csharp
private void btnLogin_Click(object sender, EventArgs e)
{
    try
    {
        if(txtPwd.Text != GlobalHelper.CurrentSystemConfig.PassWord)
        {
            // 记录登录失败日志
            LogManager.LogWarning($"用户 [{txtUserName.Text}] 登录失败：密码错误", 
                                txtUserName.Text, "用户管理", "登录");
            throw new Exception("密码错误！");
        }

        // 记录登录成功日志
        LogManager.LogInfo($"用户 [{txtUserName.Text}] 登录成功", 
                         txtUserName.Text, "用户管理", "登录");
        DialogResult = DialogResult.OK;
    }
    catch(Exception ex)
    {
        UIMessageBox.ShowError($"登录失败：{ex.Message}");
    }
}
```

### 数据库操作场景

```csharp
public static void InitializeDB()
{
    try
    {
        // 记录数据库初始化开始
        LogManager.LogInfo("开始初始化数据库", "系统", "数据库管理", "初始化");

        // 创建数据库
        CreateDB();
        
        // 表结构同步
        CreateDataTable();
        
        // 初始化表内容
        InitDataTableInfo();

        // 记录数据库初始化成功
        LogManager.LogInfo("数据库初始化完成", "系统", "数据库管理", "初始化");
    }
    catch(Exception ex)
    {
        // 记录数据库初始化失败
        LogManager.LogError($"初始化软件数据库失败：{ex.Message}", 
                          "系统", "数据库管理", "初始化", ex);
        throw;
    }
}
```

### 设备操作场景

```csharp
internal void SaveToPCDB()
{
    try
    {
        // 记录数据保存开始
        LogManager.LogInfo($"开始保存设备 [{DevInfo.SNCode}] 的数据到数据库", 
                         "系统", "数据管理", "数据保存");

        // 执行数据保存操作
        // ... 业务代码 ...

        // 记录数据保存成功
        LogManager.LogInfo($"设备 [{DevInfo.SNCode}] 的数据保存成功", 
                         "系统", "数据管理", "数据保存");
    }
    catch(Exception ex)
    {
        // 记录数据保存失败
        LogManager.LogError($"设备 [{DevInfo.SNCode}] 数据保存失败：{ex.Message}", 
                          "系统", "数据管理", "数据保存", ex);
        throw;
    }
}
```

## 配置说明

### 日志级别配置

在 SystemConfig 中的 LogConfig 可以配置日志级别：

```csharp
// 设置最小日志级别（只记录此级别及以上的日志）
SystemConfig.GetInstance().LogConfig.MinLogLevel = LogLevel.Info;

// 启用或禁用日志记录
SystemConfig.GetInstance().LogConfig.IsEnabled = true;
```

### 日志保留配置

```csharp
// 设置日志保留天数（0表示不自动清理）
SystemConfig.GetInstance().LogConfig.RetentionDays = 30;

// 设置分页大小
SystemConfig.GetInstance().LogConfig.PageSize = 50;
```

## 高级功能

### 清理过期日志

```csharp
// 异步清理过期日志
int deletedCount = await LogManager.CleanExpiredLogsAsync();
Console.WriteLine($"清理了 {deletedCount} 条过期日志");
```

### 获取日志统计信息

```csharp
// 获取日志统计信息
var stats = await LogManager.GetLogStatisticsAsync();
Console.WriteLine($"总日志数：{stats.TotalCount}");
Console.WriteLine($"今日日志数：{stats.TodayCount}");
Console.WriteLine($"错误日志数：{stats.ErrorCount}");
```

## 注意事项

1. **异步写入**：所有日志写入都是异步进行的，不会阻塞主线程
2. **异常处理**：如果数据库写入失败，会自动降级到文件日志
3. **自动信息获取**：会自动获取调用者的模块名称和IP地址
4. **配置驱动**：根据配置的日志级别决定是否记录日志
5. **线程安全**：所有方法都是线程安全的

## 最佳实践

1. **合理使用日志级别**：
   - Info：正常业务操作
   - Warning：异常但不影响系统运行的情况
   - Error：业务错误
   - Fatal：系统级致命错误

2. **提供有意义的日志内容**：
   - 包含关键参数信息
   - 描述清楚操作内容
   - 便于问题排查

3. **在关键节点记录日志**：
   - 用户登录/注销
   - 数据库操作
   - 设备通信
   - 重要业务流程的开始和结束

4. **异常处理时记录日志**：
   - 捕获异常时记录详细错误信息
   - 包含异常对象以获取完整堆栈信息
