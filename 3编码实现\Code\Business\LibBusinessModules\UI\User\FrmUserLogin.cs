﻿using LibBusinessModules.Helper;
using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.UI.User
{
    /// <summary>
    /// 用户登录界面
    /// </summary>
    partial class FrmUserLogin : UIForm
    {
        #region 构造

        public FrmUserLogin()
        {
            InitializeComponent();
            txtUserName.Text = GlobalHelper.CurrentSystemConfig.UserName;
        }

        #endregion

        #region 事件

        private void btnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                if(txtPwd.Text != GlobalHelper.CurrentSystemConfig.PassWord)
                {
                    // 记录登录失败日志
                    LogManager.LogWarning($"用户 [{txtUserName.Text}] 登录失败：密码错误", txtUserName.Text, "用户管理", "登录");
                    throw new Exception("密码错误！");
                }

                // 记录登录成功日志
                LogManager.LogInfo($"用户 [{txtUserName.Text}] 登录成功", txtUserName.Text, "用户管理", "登录");
                DialogResult = DialogResult.OK;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"登录失败：{ex.Message}");
            }
        }

        #endregion
    }
}