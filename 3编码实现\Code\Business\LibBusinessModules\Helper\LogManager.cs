using LibBaseModules.Helper;
using LibBusinessModules.Config;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace LibBusinessModules.Helper
{
    /// <summary>
    /// 系统日志管理器
    /// 提供统一的日志记录接口，支持异步写入数据库
    /// </summary>
    public static class LogManager
    {
        #region 私有字段

        /// <summary>
        /// 日志配置缓存
        /// </summary>
        private static LogConfig _logConfig;

        #endregion

        #region 静态构造函数

        /// <summary>
        /// 静态构造函数，初始化配置
        /// </summary>
        static LogManager()
        {
            RefreshConfig();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operatorName">操作人员（可选，默认获取当前用户）</param>
        /// <param name="moduleName">模块名称（可选，默认获取调用方法所在类名）</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogInfo(string content, string operatorName = null, string moduleName = null, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Info, operatorName, moduleName, operationType);
        }

        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operatorName">操作人员（可选，默认获取当前用户）</param>
        /// <param name="moduleName">模块名称（可选，默认获取调用方法所在类名）</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogWarning(string content, string operatorName = null, string moduleName = null, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Warning, operatorName, moduleName, operationType);
        }

        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operatorName">操作人员（可选，默认获取当前用户）</param>
        /// <param name="moduleName">模块名称（可选，默认获取调用方法所在类名）</param>
        /// <param name="operationType">操作类型（可选）</param>
        /// <param name="exception">异常信息（可选）</param>
        public static void LogError(string content, string operatorName = null, string moduleName = null, string operationType = "", Exception exception = null)
        {
            WriteLogAsync(content, LogLevel.Error, operatorName, moduleName, operationType, exception);
        }

        /// <summary>
        /// 记录调试级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operatorName">操作人员（可选，默认获取当前用户）</param>
        /// <param name="moduleName">模块名称（可选，默认获取调用方法所在类名）</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogDebug(string content, string operatorName = null, string moduleName = null, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Debug, operatorName, moduleName, operationType);
        }

        /// <summary>
        /// 记录致命错误级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operatorName">操作人员（可选，默认获取当前用户）</param>
        /// <param name="moduleName">模块名称（可选，默认获取调用方法所在类名）</param>
        /// <param name="operationType">操作类型（可选）</param>
        /// <param name="exception">异常信息（可选）</param>
        public static void LogFatal(string content, string operatorName = null, string moduleName = null, string operationType = "", Exception exception = null)
        {
            WriteLogAsync(content, LogLevel.Fatal, operatorName, moduleName, operationType, exception);
        }

        /// <summary>
        /// 刷新日志配置
        /// </summary>
        public static void RefreshConfig()
        {
            try
            {
                _logConfig = SystemConfig.GetInstance().LogConfig;
            }
            catch
            {
                // 如果获取配置失败，使用默认配置
                _logConfig = new LogConfig();
            }
        }



        #endregion

        #region 私有方法

        /// <summary>
        /// 异步写入日志到数据库
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="level">日志级别</param>
        /// <param name="operatorName">操作人员</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="exception">异常信息</param>
        private static void WriteLogAsync(string content, LogLevel level, string operatorName, string moduleName, string operationType, Exception exception = null)
        {
            // 检查日志级别是否需要记录
            if(!ShouldLog(level))
                return;

            Task.Run(async () =>
            {
                try
                {
                    // 获取操作人员信息
                    if(string.IsNullOrEmpty(operatorName))
                    {
                        operatorName = GetCurrentOperator();
                    }

                    // 获取模块名称
                    if(string.IsNullOrEmpty(moduleName))
                    {
                        moduleName = GetCallerModuleName();
                    }

                    // 创建日志对象
                    var logData = new SystemLogData(content, level, operatorName, moduleName, operationType);

                    // 如果有异常信息，记录异常详情
                    if(exception != null)
                    {
                        logData.ExceptionInfo = $"{exception.GetType().Name}: {exception.Message}\n{exception.StackTrace}";
                    }

                    // 写入数据库
                    using(var db = DBHelper.GetPCDBContext())
                    {
                        await db.Insertable(logData).ExecuteCommandAsync();
                    }
                }
                catch(Exception ex)
                {
                    // 如果数据库写入失败，记录到文件日志
                    LogUtil.GetInstance().LogWrite($"写入系统日志失败: {ex.Message}", LibBaseModules.Helper.MsgLevel.Error);
                }
            });
        }

        /// <summary>
        /// 判断是否需要记录该级别的日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns></returns>
        private static bool ShouldLog(LogLevel level)
        {
            return _logConfig.IsEnabled && level >= _logConfig.MinLogLevel;
        }

        /// <summary>
        /// 获取当前操作人员
        /// </summary>
        /// <returns></returns>
        private static string GetCurrentOperator()
        {
            try
            {
                // 优先从系统配置获取当前用户
                var config = SystemConfig.GetInstance();
                if(!string.IsNullOrEmpty(config.UserName))
                {
                    return config.UserName;
                }

                // 如果没有配置用户，使用系统用户名
                return Environment.UserName;
            }
            catch
            {
                return "System";
            }
        }

        /// <summary>
        /// 获取调用方法所在的模块名称
        /// </summary>
        /// <returns></returns>
        private static string GetCallerModuleName()
        {
            try
            {
                var stackTrace = new StackTrace();
                // 跳过当前方法和WriteLogAsync方法，获取实际调用者
                for(int i = 3; i < stackTrace.FrameCount; i++)
                {
                    var frame = stackTrace.GetFrame(i);
                    var method = frame?.GetMethod();
                    if(method?.DeclaringType != null && !method.DeclaringType.Name.Contains("LogManager"))
                    {
                        return method.DeclaringType.Name;
                    }
                }
                return "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 清理过期日志
        /// </summary>
        /// <returns>清理的记录数</returns>
        public static async Task<int> CleanExpiredLogsAsync()
        {
            try
            {
                var config = SystemConfig.GetInstance().LogConfig;
                if(config.RetentionDays <= 0)
                {
                    return 0; // 不清理
                }

                var expireDate = DateTime.Now.AddDays(-config.RetentionDays);

                return await Task.Run(() =>
                {
                    var db = DBHelper.GetPCDBContext();
                    return db.Deleteable<SystemLogData>()
                             .Where(x => x.LogTime < expireDate)
                             .ExecuteCommand();
                });
            }
            catch(Exception ex)
            {
                // 记录到文件日志
                LogUtil.GetInstance().LogWrite($"清理过期日志失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public static async Task<LogStatistics> GetLogStatisticsAsync()
        {
            try
            {
                return await Task.Run(() =>
                {
                    var db = DBHelper.GetPCDBContext();
                    var today = DateTime.Today;
                    var yesterday = today.AddDays(-1);
                    var thisWeek = today.AddDays(-(int)today.DayOfWeek);
                    var thisMonth = new DateTime(today.Year, today.Month, 1);

                    var stats = new LogStatistics
                    {
                        TotalCount = db.Queryable<SystemLogData>().Count(),
                        TodayCount = db.Queryable<SystemLogData>().Where(x => x.LogTime >= today).Count(),
                        YesterdayCount = db.Queryable<SystemLogData>().Where(x => x.LogTime >= yesterday && x.LogTime < today).Count(),
                        ThisWeekCount = db.Queryable<SystemLogData>().Where(x => x.LogTime >= thisWeek).Count(),
                        ThisMonthCount = db.Queryable<SystemLogData>().Where(x => x.LogTime >= thisMonth).Count(),
                        ErrorCount = db.Queryable<SystemLogData>().Where(x => x.LogLevel == LogLevel.Error).Count(),
                        WarningCount = db.Queryable<SystemLogData>().Where(x => x.LogLevel == LogLevel.Warning).Count()
                    };

                    return stats;
                });
            }
            catch(Exception ex)
            {
                LogUtil.GetInstance().LogWrite($"获取日志统计信息失败: {ex.Message}");
                throw;
            }
        }

        #endregion
    }

    /// <summary>
    /// 日志统计信息
    /// </summary>
    public class LogStatistics
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 今日记录数
        /// </summary>
        public int TodayCount { get; set; }

        /// <summary>
        /// 昨日记录数
        /// </summary>
        public int YesterdayCount { get; set; }

        /// <summary>
        /// 本周记录数
        /// </summary>
        public int ThisWeekCount { get; set; }

        /// <summary>
        /// 本月记录数
        /// </summary>
        public int ThisMonthCount { get; set; }

        /// <summary>
        /// 错误记录数
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 警告记录数
        /// </summary>
        public int WarningCount { get; set; }
    }
}
