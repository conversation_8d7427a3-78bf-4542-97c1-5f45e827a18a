using LibBaseModules.Helper;
using LibBusinessModules.Config;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.Helper;
using NPOI.HSSF.UserModel;
using SqlSugar;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using LogLevel = LibBusinessModules.DB.Models.PC.LogLevel;

namespace LibBusinessModules.UI.SystemLog
{
    /// <summary>
    /// 系统日志查看器用户控件
    /// </summary>
    public partial class UC_LogViewer : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 当前页码
        /// </summary>
        private int _currentPage = 1;

        /// <summary>
        /// 总页数
        /// </summary>
        private int _totalPages = 0;

        /// <summary>
        /// 每页记录数
        /// </summary>
        private int _pageSize = 50;

        /// <summary>
        /// 总记录数
        /// </summary>
        private int _totalRecords = 0;

        /// <summary>
        /// 日志配置
        /// </summary>
        private LogConfig _logConfig;

        /// <summary>
        /// 当前查询的日志列表
        /// </summary>
        private List<SystemLogData> _currentLogList;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public UC_LogViewer()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 获取日志配置
            _logConfig = SystemConfig.GetInstance().LogConfig;
            _pageSize = _logConfig.PageSize;

            // 初始化时间选择器
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today.AddDays(1).AddSeconds(-1);

            // 初始化日志级别下拉框
            cmbLogLevel.Items.Clear();
            cmbLogLevel.Items.Add("全部");
            foreach(LogLevel level in Enum.GetValues(typeof(LogLevel)))
            {
                cmbLogLevel.Items.Add(level.GetDescription());
            }
            cmbLogLevel.SelectedIndex = 0;

            // 更新每页记录数显示
            uiLabel7.Text = _pageSize.ToString();

            // 设置UIDataGridView的属性
            dgvLogs.AllowUserToAddRows = false;
            dgvLogs.AllowUserToDeleteRows = false;
            dgvLogs.ReadOnly = true;
            dgvLogs.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvLogs.MultiSelect = false;

            // 初始化分页控件状态
            UpdatePaginationControls();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private async void btnQuery_Click(object sender, EventArgs e)
        {
            await QueryLogsAsync();
        }

        /// <summary>
        /// 清理日志按钮点击事件
        /// </summary>
        private async void btnCleanLogs_Click(object sender, EventArgs e)
        {
            if(UIMessageBox.ShowAsk("确定要清理过期日志吗？此操作不可撤销！"))
            {
                try
                {
                    btnCleanLogs.Enabled = false;
                    btnCleanLogs.Text = "清理中...";

                    int deletedCount = await LogManager.CleanExpiredLogsAsync();
                    UIMessageBox.ShowSuccess($"成功清理 {deletedCount} 条过期日志！");

                    // 重新查询
                    await QueryLogsAsync();
                }
                catch(Exception ex)
                {
                    UIMessageBox.ShowError($"清理日志失败：{ex.Message}");
                }
                finally
                {
                    btnCleanLogs.Enabled = true;
                    btnCleanLogs.Text = "清理过期日志";
                }
            }
        }

        /// <summary>
        /// 导出Excel按钮点击事件
        /// </summary>
        private async void btnExport_Click(object sender, EventArgs e)
        {
            await ExportToExcelAsync();
        }

        /// <summary>
        /// 首页按钮点击事件
        /// </summary>
        private async void btnFirst_Click(object sender, EventArgs e)
        {
            if(_currentPage != 1)
            {
                _currentPage = 1;
                await LoadPageDataAsync();
            }
        }

        /// <summary>
        /// 上一页按钮点击事件
        /// </summary>
        private async void btnPrevious_Click(object sender, EventArgs e)
        {
            if(_currentPage > 1)
            {
                _currentPage--;
                await LoadPageDataAsync();
            }
        }

        /// <summary>
        /// 下一页按钮点击事件
        /// </summary>
        private async void btnNext_Click(object sender, EventArgs e)
        {
            if(_currentPage < _totalPages)
            {
                _currentPage++;
                await LoadPageDataAsync();
            }
        }

        /// <summary>
        /// 末页按钮点击事件
        /// </summary>
        private async void btnLast_Click(object sender, EventArgs e)
        {
            if(_currentPage != _totalPages && _totalPages > 0)
            {
                _currentPage = _totalPages;
                await LoadPageDataAsync();
            }
        }



        #endregion

        #region 数据查询和加载

        /// <summary>
        /// 查询日志数据
        /// </summary>
        private async Task QueryLogsAsync()
        {
            try
            {
                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "正在查询日志数据...");

                _currentPage = 1;

                // 构建查询条件
                var query = BuildQuery();

                // 查询总记录数
                _totalRecords = await Task.Run(() => query.Count());

                if(_totalRecords == 0)
                {
                    dgvLogs.DataSource = null;
                    _totalPages = 0;
                    UpdatePaginationControls();
                    UIMessageBox.ShowInfo("未找到符合条件的日志记录！");
                    return;
                }

                // 计算总页数
                _totalPages = (_totalRecords - 1) / _pageSize + 1;

                // 加载第一页数据
                await LoadPageDataAsync();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"查询日志失败：{ex.Message}");
            }
            finally
            {
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        /// <summary>
        /// 加载指定页的数据
        /// </summary>
        private async Task LoadPageDataAsync()
        {
            try
            {
                // 构建查询条件
                var query = BuildQuery();

                // 分页查询
                _currentLogList = await Task.Run(() =>
                    query.OrderByDescending(x => x.LogTime)
                         .ToPageList(_currentPage - 1, _pageSize));

                // 绑定数据到DataGridView
                dgvLogs.DataSource = _currentLogList;

                // 设置列标题
                SetColumnHeaders();

                // 更新分页控件
                UpdatePaginationControls();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"加载数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 构建查询条件
        /// </summary>
        private ISugarQueryable<SystemLogData> BuildQuery()
        {
            var db = DBHelper.GetPCDBContext();
            var query = db.Queryable<SystemLogData>();

            // 时间范围条件
            query = query.Where(x => x.LogTime >= dtpStartTime.Value && x.LogTime <= dtpEndTime.Value);

            // 操作人员条件
            if(!string.IsNullOrWhiteSpace(txtOperator.Text))
            {
                query = query.Where(x => x.Operator.Contains(txtOperator.Text.Trim()));
            }

            // 日志级别条件
            if(cmbLogLevel.SelectedIndex > 0)
            {
                var selectedLevel = (LogLevel)(cmbLogLevel.SelectedIndex - 1);
                query = query.Where(x => x.LogLevel == selectedLevel);
            }

            // 关键词搜索条件
            if(!string.IsNullOrWhiteSpace(txtKeyword.Text))
            {
                var keyword = txtKeyword.Text.Trim();
                query = query.Where(x => x.LogContent.Contains(keyword) ||
                                        x.ModuleName.Contains(keyword) ||
                                        x.OperationType.Contains(keyword));
            }

            return query;
        }

        /// <summary>
        /// 设置列标题
        /// </summary>
        private void SetColumnHeaders()
        {
            if(dgvLogs.Columns.Count > 0)
            {
                dgvLogs.Columns[nameof(SystemLogData.LogId)].HeaderText = "日志ID";
                dgvLogs.Columns[nameof(SystemLogData.LogTime)].HeaderText = "日志时间";
                dgvLogs.Columns[nameof(SystemLogData.LogLevel)].HeaderText = "日志级别";
                dgvLogs.Columns[nameof(SystemLogData.Operator)].HeaderText = "操作人员";
                dgvLogs.Columns[nameof(SystemLogData.ModuleName)].HeaderText = "模块名称";
                dgvLogs.Columns[nameof(SystemLogData.OperationType)].HeaderText = "操作类型";
                dgvLogs.Columns[nameof(SystemLogData.LogContent)].HeaderText = "日志内容";
                dgvLogs.Columns[nameof(SystemLogData.ExceptionInfo)].HeaderText = "异常信息";

                // 设置列宽
                dgvLogs.Columns[nameof(SystemLogData.LogId)].Width = 80;
                dgvLogs.Columns[nameof(SystemLogData.LogTime)].Width = 150;
                dgvLogs.Columns[nameof(SystemLogData.LogLevel)].Width = 80;
                dgvLogs.Columns[nameof(SystemLogData.Operator)].Width = 100;
                dgvLogs.Columns[nameof(SystemLogData.ModuleName)].Width = 120;
                dgvLogs.Columns[nameof(SystemLogData.OperationType)].Width = 100;
                dgvLogs.Columns[nameof(SystemLogData.LogContent)].Width = 300;
                dgvLogs.Columns[nameof(SystemLogData.ExceptionInfo)].Width = 200;

                // 隐藏不需要显示的列
                if(dgvLogs.Columns[nameof(SystemLogData.ExceptionInfo)] != null)
                {
                    dgvLogs.Columns[nameof(SystemLogData.ExceptionInfo)].Visible = false;
                }
            }
        }

        /// <summary>
        /// 更新分页控件状态
        /// </summary>
        private void UpdatePaginationControls()
        {
            lblPageInfo.Text = $"第 {_currentPage} 页，共 {_totalPages} 页，总计 {_totalRecords} 条记录";

            btnFirst.Enabled = _currentPage > 1;
            btnPrevious.Enabled = _currentPage > 1;
            btnNext.Enabled = _currentPage < _totalPages;
            btnLast.Enabled = _currentPage < _totalPages;
        }

        /// <summary>
        /// 导出到Excel
        /// </summary>
        private async Task ExportToExcelAsync()
        {
            try
            {
                if(_currentLogList == null || _currentLogList.Count == 0)
                {
                    UIMessageBox.ShowWarning("没有数据可以导出！");
                    return;
                }

                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel文件|*.xls",
                    FileName = $"系统日志_{DateTime.Now:yyyyMMdd_HHmmss}.xls"
                };

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    UIFormServiceHelper.ShowWaitForm(this.ParentForm, "正在导出Excel文件...");

                    await Task.Run(() =>
                    {
                        // 创建工作簿
                        var workbook = new HSSFWorkbook();
                        var sheet = workbook.CreateSheet("系统日志");

                        // 创建标题行
                        var headerRow = sheet.CreateRow(0);
                        var headers = new[] { "日志ID", "日志时间", "日志级别", "操作人员", "模块名称", "操作类型", "日志内容", "客户端IP" };
                        for(int i = 0; i < headers.Length; i++)
                        {
                            headerRow.CreateCell(i).SetCellValue(headers[i]);
                        }

                        // 填充数据
                        for(int i = 0; i < _currentLogList.Count; i++)
                        {
                            var dataRow = sheet.CreateRow(i + 1);
                            var log = _currentLogList[i];

                            dataRow.CreateCell(0).SetCellValue(log.LogId);
                            dataRow.CreateCell(1).SetCellValue(log.LogTime.ToString("yyyy-MM-dd HH:mm:ss"));
                            dataRow.CreateCell(2).SetCellValue(log.LogLevel.GetDescription());
                            dataRow.CreateCell(3).SetCellValue(log.Operator ?? "");
                            dataRow.CreateCell(4).SetCellValue(log.ModuleName ?? "");
                            dataRow.CreateCell(5).SetCellValue(log.OperationType ?? "");
                            dataRow.CreateCell(6).SetCellValue(log.LogContent ?? "");
                        }

                        // 自动调整列宽
                        for(int i = 0; i < headers.Length; i++)
                        {
                            sheet.AutoSizeColumn(i);
                        }

                        // 保存文件
                        using(var fileStream = new FileStream(saveFileDialog.FileName, FileMode.Create))
                        {
                            workbook.Write(fileStream);
                        }
                    });

                    UIMessageBox.ShowSuccess("Excel文件导出成功！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"导出Excel失败：{ex.Message}");
            }
            finally
            {
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        #endregion
    }
}
