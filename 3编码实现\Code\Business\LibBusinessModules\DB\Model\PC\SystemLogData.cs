using LibBaseModules.DB;
using SqlSugar;
using System;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 系统日志数据
    /// </summary>
    [SugarTable("system_log")]
    [SugarIndex("index_log_time", nameof(LogTime), OrderByType.Desc)]
    [SugarIndex("index_log_level", nameof(LogLevel), OrderByType.Asc)]
    [SugarIndex("index_operator", nameof(Operator), OrderByType.Asc)]
    [Description("系统日志数据")]
    public class SystemLogData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 日志ID（主键，自增长）
        /// </summary>
        [SugarColumn(ColumnName = "log_id", ColumnDescription = "日志ID", IsPrimaryKey = true, IsIdentity = true)]
        [Description("日志ID")]
        public int LogId { get; set; }

        /// <summary>
        /// 日志时间
        /// </summary>
        [SugarColumn(ColumnName = "log_time", SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "日志时间")]
        [Description("日志时间")]
        public DateTime LogTime { get; set; }

        /// <summary>
        /// 日志内容
        /// </summary>
        [SugarColumn(ColumnName = "log_content", ColumnDataType = "TEXT", ColumnDescription = "日志内容")]
        [Description("日志内容")]
        public string LogContent { get; set; }

        /// <summary>
        /// 操作人员
        /// </summary>
        [SugarColumn(ColumnName = "operator", ColumnDescription = "操作人员")]
        [Description("操作人员")]
        public string Operator { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        [SugarColumn(ColumnName = "log_level", ColumnDescription = "日志级别")]
        [Description("日志级别")]
        public LogLevel LogLevel { get; set; }

        /// <summary>
        /// 模块名称
        /// </summary>
        [SugarColumn(ColumnName = "module_name", ColumnDescription = "模块名称")]
        [Description("模块名称")]
        public string ModuleName { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [SugarColumn(ColumnName = "operation_type", ColumnDescription = "操作类型")]
        [Description("操作类型")]
        public string OperationType { get; set; }

        /// <summary>
        /// 异常信息（可选）
        /// </summary>
        [SugarColumn(ColumnName = "exception_info", ColumnDataType = "TEXT", ColumnDescription = "异常信息", IsNullable = true)]
        [Description("异常信息")]
        public string ExceptionInfo { get; set; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SystemLogData()
        {
            LogTime = DateTime.Now;
            LogLevel = LogLevel.Info;
        }

        /// <summary>
        /// 带参数构造函数
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="level">日志级别</param>
        /// <param name="operatorName">操作人员</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="operationType">操作类型</param>
        public SystemLogData(string content, LogLevel level, string operatorName, string moduleName = "", string operationType = "")
        {
            LogTime = DateTime.Now;
            LogContent = content;
            LogLevel = level;
            Operator = operatorName;
            ModuleName = moduleName;
            OperationType = operationType;
        }

        #endregion
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        [Description("信息")]
        Info = 0,

        /// <summary>
        /// 警告
        /// </summary>
        [Description("警告")]
        Warning = 1,

        /// <summary>
        /// 错误
        /// </summary>
        [Description("错误")]
        Error = 2,

        /// <summary>
        /// 调试
        /// </summary>
        [Description("调试")]
        Debug = 3,

        /// <summary>
        /// 致命错误
        /// </summary>
        [Description("致命错误")]
        Fatal = 4
    }
}
